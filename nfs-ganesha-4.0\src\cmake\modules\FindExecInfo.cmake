FIND_PATH(EXECINFO_INCLUDE_DIR execinfo.h)
FIND_LIBRARY(EXECINFO_LIBRARY NAMES execinfo)

IF (EXECINFO_INCLUDE_DIR AND EXECINFO_LIBRARY)
  SET(EXECIN<PERSON><PERSON>_FOUND TRUE)
ENDIF (EXECINFO_INCLUDE_DIR AND <PERSON>X<PERSON><PERSON>FO_LIBRARY)

IF (EXECINFO_FOUND)
  IF (NOT EXECINFO_FIND_QUIETLY)
    MESSAGE(STATUS "Found execinfo library: ${EXECINFO_LIBRARY}")
  ENDIF (NOT EXECINFO_FIND_QUIETLY)
ELSE (EXECINFO_FOUND)
  IF (ExecInfo_FIND_REQUIRED)
    MESSAGE(FATAL_ERROR "Could not find libexecinfo")
  ENDIF (ExecInfo_FIND_REQUIRED)
ENDIF (EXECINFO_FOUND)
