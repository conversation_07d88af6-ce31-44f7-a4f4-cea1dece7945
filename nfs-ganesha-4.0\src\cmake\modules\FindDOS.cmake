# - Find DOS
# Find the Linux Trace Toolkit - next generation with associated includes path.
# See http://idfs.org/
#
# This module accepts the following optional variables:
#    DOS_PREFIX   = A hint on DOS install path.
#
# This module defines the following variables:
#    DOS_FOUND       = Was DOS found or not?
#    DOS_LIBRARIES   = The list of libraries to link to when using DOS
#    DOS_INCLUDE_DIR = The path to DOS include directory
#
# On can set DOS_PREFIX before using find_package(DOS) and the
# module with use the PATH as a hint to find DOS.
#
# The hint can be given on the command line too:
#   cmake -DDOS_PREFIX=/DATA/ERIC/DOS /path/to/source

if(DOS_PREFIX)
  message(STATUS "FindDOS: using PATH HINT: ${DOS_PREFIX}")
  # Try to make the prefix override the normal paths
  find_path(DOS_INCLUDE_DIR
    NAMES include/uds/libdos.h
    PATHS ${DOS_PREFIX}
    NO_DEFAULT_PATH
    DOC "The DOS include headers")
    message("DOS_INCLUDE_DIR ${DOS_INCLUDE_DIR}")

  find_path(DOS_LIBRARY_DIR
    NAMES libdos.so
    PATHS ${DOS_PREFIX}
    PATH_SUFFIXES lib/${CMAKE_LIBRARY_ARCHITECTURE} lib lib64
    NO_DEFAULT_PATH
    DOC "The DOS libraries")
endif()

if (NOT DOS_INCLUDE_DIR)
  find_path(DOS_INCLUDE_DIR
    NAMES include/uds/libdos.h
    PATHS ${DOS_PREFIX}
    DOC "The DOS include headers")
endif (NOT DOS_INCLUDE_DIR)

if (NOT DOS_LIBRARY_DIR)
  find_path(DOS_LIBRARY_DIR
    NAMES libdos.so
    PATHS ${DOS_PREFIX}
    PATH_SUFFIXES lib/${CMAKE_LIBRARY_ARCHITECTURE} lib lib64
    DOC "The DOS libraries")
endif (NOT DOS_LIBRARY_DIR)

find_library(DOS_LIBRARY dos PATHS ${DOS_LIBRARY_DIR} NO_DEFAULT_PATH)
check_library_exists(dos dos_mount ${DOS_LIBRARY_DIR} DOSLIB)
if (NOT DOSLIB)
  unset(DOS_LIBRARY_DIR CACHE)
  unset(DOS_INCLUDE_DIR CACHE)
else (NOT DOSLIB)
  check_library_exists(dos dos_mount2 ${DOS_LIBRARY_DIR} DOS_MOUNT2)
  if(NOT DOS_MOUNT2)
    message("Cannot find dos_mount2. Fallback to use dos_mount")
    set(USE_FSAL_DOS_MOUNT2 OFF)
  else(DOS_MOUNT2)
    set(USE_FSAL_DOS_MOUNT2 ON)
  endif(NOT DOS_MOUNT2)
  check_library_exists(dos dos_getxattrs ${DOS_LIBRARY_DIR} DOS_XATTRS)
  if(NOT DOS_XATTRS)
    message("Cannot find xattrs")
    set(USE_FSAL_DOS_XATTRS OFF)
  else(DOS_XATTRS)
    set(USE_FSAL_DOS_XATTRS ON)
  endif(NOT DOS_XATTRS)
endif (NOT DOSLIB)

set(DOS_LIBRARIES ${DOS_LIBRARY})
message(STATUS "Found dos libraries: ${DOS_LIBRARIES}")

set(DOS_FILE_HEADER "${DOS_INCLUDE_DIR}/include/uds/dos_file.h")
if (EXISTS ${DOS_FILE_HEADER})
  file(STRINGS ${DOS_FILE_HEADER} DOS_MAJOR REGEX
    "LIBDOS_FILE_VER_MAJOR (\\d*).*$")
  string(REGEX REPLACE ".+LIBDOS_FILE_VER_MAJOR (\\d*)" "\\1" DOS_MAJOR
    "${DOS_MAJOR}")

  file(STRINGS ${DOS_FILE_HEADER} DOS_MINOR REGEX
    "LIBDOS_FILE_VER_MINOR (\\d*).*$")
  string(REGEX REPLACE ".+LIBDOS_FILE_VER_MINOR (\\d*)" "\\1" DOS_MINOR
    "${DOS_MINOR}")

  file(STRINGS ${DOS_FILE_HEADER} DOS_EXTRA REGEX
    "LIBDOS_FILE_VER_EXTRA (\\d*).*$")
  string(REGEX REPLACE ".+LIBDOS_FILE_VER_EXTRA (\\d*)" "\\1" DOS_EXTRA
    "${DOS_EXTRA}")

  set(DOS_FILE_VERSION "${DOS_MAJOR}.${DOS_MINOR}.${DOS_EXTRA}")
else()
  set(DOS_FILE_VERSION "0.0.0")
endif()

# handle the QUIETLY and REQUIRED arguments and set PRELUDE_FOUND to TRUE if
# all listed variables are TRUE
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(DOS
  REQUIRED_VARS DOS_INCLUDE_DIR DOS_LIBRARY_DIR
  VERSION_VAR DOS_FILE_VERSION
  )
# VERSION FPHSA options not handled by CMake version < 2.8.2)
#                                  VERSION_VAR)

mark_as_advanced(DOS_INCLUDE_DIR)
mark_as_advanced(DOS_LIBRARY_DIR)
