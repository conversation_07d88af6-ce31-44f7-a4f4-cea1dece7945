/*
 * Copyright © Red Hat 2015
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

/**
 * @file   internal.h
 * @brief Internal declarations for the DOS FSAL
 *
 * This file includes declarations of data types, functions,
 * variables, and constants for the DOS FSAL.
 */

#ifndef FSAL_DOS_INTERNAL_INTERNAL
#define FSAL_DOS_INTERNAL_INTERNAL

#include <stdbool.h>
#include <uuid/uuid.h>
#include <dirent.h> /* NAME_MAX */

#include "fsal.h"
#include "fsal_types.h"
#include "fsal_api.h"
#include "fsal_convert.h"
#include "sal_data.h"

#include <include/uds/libdos.h>
#include <include/uds/dos_file.h>

#if (!GSH_CHECK_VERSION(1, 1, 7, LIBDOS_FILE_VER_MAJOR, LIBDOS_FILE_VER_MINOR, \
			LIBDOS_FILE_VER_EXTRA))
#error uds/dos_file.h version unsupported (require >= 1.1.7)
#endif

/**
 * DOS Main (global) module object
 */

struct dos_fsal_module {
	struct fsal_module fsal;
	struct fsal_obj_ops handle_ops;
	char *conf_path;
	char *name;
	char *cluster;
	char *init_args;
	libdos_t dos;
};
extern struct dos_fsal_module DOSFSM;


#define MAXUIDLEN 32
#define MAXKEYLEN 20
#define MAXSECRETLEN 40

/**
 * DOS internal export object
 */

struct dos_export {
	struct fsal_export export;	/*< The public export object */
	struct dos_fs *dos_fs;		/*< "Opaque" fs handle */
	struct dos_handle *root;    /*< root handle */
	char *dos_name;
	char *dos_user_id;
	char *dos_access_key_id;
	char *dos_secret_access_key;
};

/**
 * The DOS FSAL internal handle
 */

struct dos_handle {
	struct fsal_obj_handle handle;	/*< The public handle */
	struct dos_file_handle *dos_fh;  /*< DOS-internal file handle */
	/* XXXX remove ptr to up-ops--we can always follow export! */
	const struct fsal_up_vector *up_ops;	/*< Upcall operations */
	struct dos_export *export;	/*< The first export this handle
					 *< belongs to */
	struct fsal_share share;
	fsal_openflags_t openflags;
};

/**
 * DOS "file descriptor"
 */
struct dos_open_state {
	struct state_t gsh_open;
	fsal_openflags_t openflags;
};

/**
 * The attributes this FSAL can interpret or supply.
 * Currently FSAL_DOS uses posix2fsal_attributes, so we should indicate support
 * for at least those attributes.
 */
#define DOS_SUPPORTED_ATTRIBUTES ((ATTRS_POSIX | ATTR4_XATTR))

/**
 * The attributes this FSAL can set.
 */
#define DOS_SETTABLE_ATTRIBUTES ((const attrmask_t) (			\
	ATTR_MODE  | ATTR_OWNER | ATTR_GROUP | ATTR_ATIME	 |\
	ATTR_CTIME | ATTR_MTIME | ATTR_SIZE  | ATTR_MTIME_SERVER |\
	ATTR_ATIME_SERVER | ATTR4_XATTR))

/**
 * Linux supports a stripe pattern with no more than 4096 stripes, but
 * for now we stick to 1024 to keep them da_addrs from being too
 * gigantic.
 */

static const size_t BIGGEST_PATTERN = 1024;

/* Prototypes */
int construct_handle(struct dos_export *export,
		     struct dos_file_handle *dos_file_handle,
		     struct stat *st,
		     struct dos_handle **obj);
void deconstruct_handle(struct dos_handle *obj);

fsal_status_t dos2fsal_error(const int errorcode);
void export_ops_init(struct export_ops *ops);
void handle_ops_init(struct fsal_obj_ops *ops);
struct state_t *dos_alloc_state(struct fsal_export *exp_hdl,
				enum state_type state_type,
				struct state_t *related_state);
void dos_fs_invalidate(void *handle, struct dos_fh_hk fh_hk);
#endif				/* !FSAL_DOS_INTERNAL_INTERNAL */
