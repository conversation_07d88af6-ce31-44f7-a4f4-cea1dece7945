FIND_PATH(NFSAUDIT_INCLUDE_DIR audit/idfsAuditLog.h)
MESSAGE(STATUS "Found  INCLUDE_DIR**********: ${NFSAUDIT_INCLUDE_DIR}")

FIND_LIBRARY(NFSAUDIT_LIBRARY NAMES libidfsauditlog.so )
MESSAGE(STATUS "Found NFSAUDIT_LIBRARY********: ${NFSAUDIT_LIBRARY}")


IF (NFSAUDIT_INCLUDE_DIR AND NFSAUDIT_LIBRARY)
  SET(NFSAUDIT_FOUND TRUE)
ENDIF (NFSAUDIT_INCLUDE_DIR AND NFSAUDIT_LIBRARY)

IF (NFSAUDIT_FOUND)
  IF (NOT NFSAUDIT_FIND_QUIETLY)
    MESSAGE(STATUS "Found nfs libidfsauditlog library: ${NFSAUDIT_LIBRARY}")
  ENDIF (NOT NFSAUDIT_FIND_QUIETLY)
ELSE (NFSAUDIT_FOUND)
  IF (NFSAUDIT_FIND_REQUIRED)
    MESSAGE(FATAL_ERROR "Could not find libidfsauditlog")
  ENDIF (NFSAUDIT_FIND_REQUIRED)
ENDIF (NFSAUDIT_FOUND)