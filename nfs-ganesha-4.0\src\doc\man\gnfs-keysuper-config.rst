===================================================================
gnfs-keysuper-config -- NFS Gnfs KeySuper Configuration File
===================================================================

.. program:: gnfs-keysuper-config


SYNOPSIS
==========================================================

| /etc/gnfs/keysuper.conf

DESCRIPTION
==========================================================

Gnfs installs the following sample config file for KeySuper FSAL:
| /etc/gnfs/keysuper.conf

This file lists KeySuper specific config options.

EXPORT { FSAL {} }
--------------------------------------------------------------------------------
Name(string, "KEYSUPER")
    Name of FSAL should always be KEYSUPER.

**volume(string, no default, required)**

**hostname(string, no default, required)**

**volpath(path, default "/")**

**glfs_log(path, default "/var/log/gnfs/gnfs-gfapi.log")**

**up_poll_usec(uint64, range 1 to 60*1000*1000, default 10)**

**enable_upcall(bool, default true)**

**transport(enum, values [tcp, rdma], default tcp)**

**sec_label_xattr(char, default "security.selinux xattr of the file")**

KEYSUPER {}
--------------------------------------------------------------------------------

**PNFS_DMS(bool, default FALSE)**
  Set this parameter to true to select this node as DMS

See also
==============================
:doc:`gnfs-log-config <gnfs-log-config>`\(8)
:doc:`gnfs-core-config <gnfs-core-config>`\(8)
:doc:`gnfs-export-config <gnfs-export-config>`\(8)
