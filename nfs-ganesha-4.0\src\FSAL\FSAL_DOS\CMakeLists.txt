add_definitions(
  -D_FILE_OFFSET_BITS=64
)

SET(fsaldos_LIB_SRCS
  up.c
  main.c
  export.c
  handle.c
  internal.c
  internal.h
)

message("DOS_INCLUDE_DIR ${DOS_INCLUDE_DIR}")
include_directories(${DOS_INCLUDE_DIR})

add_library(fsaldos MODULE ${fsaldos_LIB_SRCS})
add_sanitizers(fsaldos)

target_link_libraries(fsaldos
  gnfs_nfsd
  ${DOS_LIBRARIES}
  ${SYSTEM_LIBRARIES}
  ${LDFLAG_DISALLOW_UNDEF}
)

set_target_properties(fsaldos PROPERTIES VERSION 4.2.0 SOVERSION 4)
install(TARGETS fsaldos COMPONENT fsal DESTINATION ${FSAL_DESTINATION} )

########### install files ###############
