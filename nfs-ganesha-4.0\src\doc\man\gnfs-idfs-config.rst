===================================================================
gnfs-idfs-config -- NFS Gnfs IDFS Configuration File
===================================================================

.. program:: gnfs-idfs-config


SYNOPSIS
==========================================================

| /etc/gnfs/idfs.conf

DESCRIPTION
==========================================================

Gnfs install config example for IDFS FSAL:
| /etc/gnfs/idfs.conf

This file lists IDFS specific config options.

EXPORT { FSAL {} }
--------------------------------------------------------------------------------
Name(string, "Idfs")
    Name of FSAL should always be Idfs.

Filesystem(string, no default)
    Idfs filesystem name string, for mounting an alternate filesystem within
    the cluster. The default is to mount the default filesystem in the cluster
    (usually, the first one created).

User_Id(string, no default)
    idfsx userid used to open the DMS session. This string is what gets appended
    to "client.". If not set, the idfs client libs will sort this out based on
    idfs configuration.

Secret_Access_Key(string, no default)
    Key to use for the session (if any). If not set, then it uses the normal
    search path for idfsx keyring files to find a key.

IDFS {}
--------------------------------------------------------------------------------

**Idfs_Conf(path, default "")**

**umask(mode, range 0 to 0777, default 0)**

See also
==============================
:doc:`gnfs-config <gnfs-config>`\(8)
:doc:`gnfs-log-config <gnfs-log-config>`\(8)
:doc:`gnfs-core-config <gnfs-core-config>`\(8)
:doc:`gnfs-export-config <gnfs-export-config>`\(8)

