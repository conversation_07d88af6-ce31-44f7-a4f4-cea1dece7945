===================================================================
gnfs-gpfs-config -- NFS Gnfs GPFS Configuration File
===================================================================

.. program:: gnfs-gpfs-config


SYNOPSIS
==========================================================

| /etc/gnfs/gpfs.conf

DESCRIPTION
==========================================================

Gnfs install the following config file for GPFS FSAL:
| /etc/gnfs/gpfs.conf

This file lists GPFS specific config options.

GPFS {}
--------------------------------------------------------------------------------

**link_support(bool, default true)**

**symlink_support(bool, default true)**

**cansettime(bool, default true)**

**umask(mode, range 0 to 0777, default 0)**

**auth_xdev_export(bool, default false)**

**Delegations(enum, default read)**

  Possible values:
	None, read, write, readwrite, r, w, rw

**pnfs_file(bool, default false)**

**fsal_trace(bool, default true)**

**fsal_grace(bool, default false)**

See also
==============================
:doc:`gnfs-log-config <gnfs-log-config>`\(8)
:doc:`gnfs-core-config <gnfs-core-config>`\(8)
:doc:`gnfs-export-config <gnfs-export-config>`\(8)
