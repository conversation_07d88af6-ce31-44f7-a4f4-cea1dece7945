===================================================================
gnfs-config -- NFS Gnfs Configuration File
===================================================================

.. program:: gnfs-config


SYNOPSIS
==========================================================

| /etc/gnfs/gnfs.conf

DESCRIPTION
==========================================================

Gnfs obtains configuration data from the configuration file:

    /etc/gnfs/gnfs.conf

The configuration file consists of following parts:

Comments
--------------------------------------------------------------------------------
Empty lines and lines starting with '#' are comments.::

    # This whole line is a comment
    Protocol = TCP; # The rest of this line is a comment

Blocks
--------------------------------------------------------------------------------
Related options are grouped together into "blocks".
A block is a name followed by parameters enclosed between "{"
and "}".
A block can contain other sub blocks as well.::

    EXPORT
    {
        Export_ID = 1;
        FSAL {
            Name = VFS:
        }
    }

NOTE: FSAL is a sub block.
Refer to ``BLOCKS`` section for list of blocks and options.

Options
--------------------------------------------------------------------------------
Configuration options can be of following types.

1. **Numeric.** Numeric options can be defined in octal, decimal, or hexadecimal.
The format follows ANSI C syntax.
eg.::

    mode = 0755;  # This is octal 0755, 493 (decimal)

Numeric values can also be negated or logical NOT'd.
eg.::

    anonymousuid = -2; # this is a negative
    mask = ~0xff; # Equivalent to 0xffffff00 (for 32 bit integers)

2. **Boolean.** Possible values are true, false, yes and no.
1 and 0 are not acceptable.

3. **List.** The option can contain a list of possible applicable values.
Protocols = 3, 4, 9p;


Including other config files
--------------------------------------------------------------------------------
Additional files can be referenced in a configuration using '%include'
and '%url' directives.::

	%include <filename>
	%url <url, e.g., uds://mypool/mynamespace/myobject>

The included file is inserted into the configuration text in place of
the %include or %url line. Sub-inclusions may be to any depth. Filenames and
URLs may optionally use '"'::

    %include base.conf
    %include "base.conf"
    %url uds://mypool/mynamespace/myobject
    %url "uds://mypool/mynamespace/myobject"
    %url uds://mypool/myobject
    %url "uds://mypool/myobject"

In the case of uds:// URLs, providing a two-component URL indicates that
the default namespace should be used.


Reloading Config
--------------------------------------------------------------------------------
A config reload can be triggered by sending the gnfs.nfsd process a SIGHUP.
Not all config options may be changed with reload, those that can will be
documented in the individual sections.

In general, currently dynamic config is supported for EXPORT and LOG options.


BLOCKS
==========================================================
Gnfs supports the following blocks:

EXPORT {}
--------------------------------------------------------------------------------
Along with its configuration options, the **EXPORT** block supports **FSAL**
and **CLIENT** sub-blocks. See
:doc:`gnfs-export-config <gnfs-export-config>`\(8) for usage of this
block and its sub-blocks.

EXPORT_DEFAULTS {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-export-config <gnfs-export-config>`\(8) for usage

MDCACHE {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-cache-config <gnfs-cache-config>`\(8) for usage

NFS_CORE_PARAM {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-core-config <gnfs-core-config>`\(8) for usage

NFS_IP_NAME {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-core-config <gnfs-core-config>`\(8) for usage

NFS_KRB5 {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-core-config <gnfs-core-config>`\(8) for usage

NFSv4 {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-core-config <gnfs-core-config>`\(8) for usage

IDFS {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-idfs-config <gnfs-idfs-config>`\(8) for usage

9P {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-9p-config <gnfs-9p-config>`\(8) for usage

KEYSUPER {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-keysuper-config <gnfs-keysuper-config>`\(8) for usage

GPFS {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-gpfs-config <gnfs-gpfs-config>`\(8) for usage

LOG {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-log-config <gnfs-log-config>`\(8) for usage

1.**LOG { FACILITY {} }**
2.**LOG { FORMAT {} }**

PROXY_V4 {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-proxy-config <gnfs-proxy-v4-config>`\(8) for usage

PROXY_V3 {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-proxy-v3-config <gnfs-proxy-v3-config>`\(8) for usage

DOS {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-dos-config <gnfs-dos-config>`\(8) for usage

VFS {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-vfs-config <gnfs-vfs-config>`\(8) for usage

XFS {}
--------------------------------------------------------------------------------
Refer to :doc:`gnfs-xfs-config <gnfs-xfs-config>`\(8) for usage


EXAMPLE
==========================================================
Along with "gnfs.conf", for each installed FSAL, a sample config file is added at:

| /etc/gnfs


See also
==============================
:doc:`gnfs-log-config <gnfs-log-config>`\(8)
:doc:`gnfs-dos-config <gnfs-dos-config>`\(8)
:doc:`gnfs-vfs-config <gnfs-vfs-config>`\(8)
:doc:`gnfs-lustre-config <gnfs-lustre-config>`\(8)
:doc:`gnfs-xfs-config <gnfs-xfs-config>`\(8)
:doc:`gnfs-gpfs-config <gnfs-gpfs-config>`\(8)
:doc:`gnfs-keysuper-config <gnfs-keysuper-config>`\(8)
:doc:`gnfs-9p-config <gnfs-9p-config>`\(8)
:doc:`gnfs-proxy-config <gnfs-proxy-config>`\(8)
:doc:`gnfs-proxy-v3-config <gnfs-proxy-v3-config>`\(8)
:doc:`gnfs-idfs-config <gnfs-idfs-config>`\(8)
:doc:`gnfs-core-config <gnfs-core-config>`\(8)
:doc:`gnfs-export-config <gnfs-export-config>`\(8)
