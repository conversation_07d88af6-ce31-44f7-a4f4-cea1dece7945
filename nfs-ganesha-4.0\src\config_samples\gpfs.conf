###################################################
#
# EXPORT_DEFAULTS Parameter
#
###################################################

EXPORT_DEFAULTS
{
	# GPFS has an invalidate upcall that allows it
	# to trust attributes longer.
	Attr_Expiration_Time = 600;
}

###################################################
#
#  NFS_Core_Param
#
###################################################

NFS_Core_Param
{
	# GPFS is clustered
	Clustered = TRUE;
}

###################################################
#
# NFSv4 Specific configuration stuff
#
###################################################
NFSv4
{
	Lease_Lifetime=90;
}

###################################################
#
# FSAL Specific config to enable READ delegation support.
#
###################################################

GPFS
{
  delegations = R;
}

###################################################
#     Export entries
###################################################


# First export entry

EXPORT
{
	Export_Id = 77;
	Path = /ibm/gpfs0;
	Pseudo = /ibm/gpfs0;

	Access_Type=RW;

	FSAL {
		Name = GPFS;
	}
}
