EXPORT
{
	Export_ID=1;

	Path = "/";

	Pseudo = "/";

	Access_Type = RW;

	Protocols = 4;

	Transports = TCP;

	FSAL {
		Name = DOS;
		User_Id = "testuser";
		Access_Key_Id ="<substitute yours>";
		Secret_Access_Key = "<substitute yours>";
	}
}

DOS {
	idfs_conf = "/<substitute path to>/idfs.conf";
	# for vstart cluster, name = "client.admin"
	name = "client.dos.foohost";
	cluster = "idfs";
#	init_args = "-d --debug-dos=16";
}
