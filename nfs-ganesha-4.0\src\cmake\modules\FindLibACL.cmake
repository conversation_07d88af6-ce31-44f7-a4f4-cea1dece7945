FIND_PATH(<PERSON><PERSON><PERSON><PERSON>_INCLUDE_DIR acl/libacl.h)
FIND_LIBRARY(LIBACL_LIBRARY NAMES acl)

IF (LIBACL_INCLUDE_DIR AND LIBACL_LIBRARY)
  SET(L<PERSON><PERSON><PERSON>_FOUND TRUE)
ENDIF (<PERSON><PERSON><PERSON><PERSON>_INCLUDE_DIR AND <PERSON><PERSON><PERSON><PERSON>_LIBRARY)

IF (LIBACL_FOUND)
  IF (NOT LIBACL_FIND_QUIETLY)
    MESSAGE(STATUS "Found ACL library: ${LIBACL_LIBRARY}")
  ENDIF (NOT LIBACL_FIND_QUIETLY)
ELSE (LIBACL_FOUND)
  IF (LibACL_FIND_REQUIRED)
    MESSAGE(FATAL_ERROR "Could not find libacl")
  ENDIF (LibACL_FIND_REQUIRED)
ENDIF (LIBACL_FOUND)
