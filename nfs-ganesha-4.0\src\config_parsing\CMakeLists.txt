find_package(BISON)
find_package(FLEX)

add_definitions(
  -D__USE_GNU
)

BISON_TARGET(
  ConfigParser
  ${CMAKE_CURRENT_SOURCE_DIR}/conf_yacc.y
  ${CMAKE_CURRENT_BINARY_DIR}/conf_yacc.c
  COMPILE_FLAGS "--defines -pgnfs_yy"
)

FLEX_TARGET(
  ConfigScanner
  ${CMAKE_CURRENT_SOURCE_DIR}/conf_lex.l
  ${CMAKE_CURRENT_BINARY_DIR}/conf_lex.c
  COMPILE_FLAGS "-Pganeshun_yy -olex.yy.c"
)

ADD_FLEX_BISON_DEPENDENCY(ConfigScanner ConfigParser)

include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_CURRENT_BINARY_DIR})

########### next target ###############

SET(config_parsing_STAT_SRCS
   analyse.c
   config_parsing.c
   conf_url.c
   analyse.h
)

add_library(config_parsing OBJECT
  ${config_parsing_STAT_SRCS}
  ${BISON_ConfigParser_OUTPUTS}
  ${FLEX_ConfigScanner_OUTPUTS}
)

add_sanitizers(config_parsing)
set_target_properties(config_parsing PROPERTIES COMPILE_FLAGS "-fPIC")

if(UDS_URLS)
  set(config_parsing_UDS_SRCS
    ${config_parsing_UDS_SRCS}
    conf_url_uds.c
    )
  add_library(gnfs_uds_urls MODULE ${config_parsing_UDS_SRCS})
  add_sanitizers(gnfs_uds_urls)
  target_link_libraries(gnfs_uds_urls
    gnfs_nfsd
    ${SYSTEM_LIBRARIES}
    ${UDS_LIBRARIES}
    ${LDFLAG_DISALLOW_UNDEF})
  include_directories(${UDS_INCLUDE_DIR})
  set_target_properties(gnfs_uds_urls PROPERTIES SOVERSION
    "${GNFS_MAJOR_VERSION}${GNFS_MINOR_VERSION}")
  install(TARGETS gnfs_uds_urls LIBRARY DESTINATION ${LIB_INSTALL_DIR})

endif(UDS_URLS)

########### install files ###############
