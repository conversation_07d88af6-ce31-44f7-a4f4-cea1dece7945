set(sphinx_output_dir ${CMAKE_BINARY_DIR}/doc)

set(man_srcs
    gnfs-config.rst
    gnfs-log-config.rst
    gnfs-cache-config.rst
    gnfs-export-config.rst
    gnfs-core-config.rst)

if(USE_9P)
   list(APPEND man_srcs
        gnfs-9p-config.rst)
endif()

if(USE_FSAL_IDFS)
   list(APPEND man_srcs
        gnfs-idfs-config.rst)
endif()

if(USE_FSAL_DOS)
   list(APPEND man_srcs
        gnfs-dos-config.rst)
endif()

if(USE_FSAL_XFS)
   list(APPEND man_srcs
        gnfs-xfs-config.rst)
endif()

if(USE_FSAL_KEYSUPER)
   list(APPEND man_srcs
        gnfs-keysuper-config.rst)
endif()

if(USE_FSAL_VFS)
   list(APPEND man_srcs
        gnfs-vfs-config.rst)
endif()

if(USE_FSAL_LUSTRE)
   list(APPEND man_srcs
        gnfs-lustre-config.rst)
endif()

if(USE_FSAL_PROXY_V4)
   list(APPEND man_srcs
        gnfs-proxy-v4-config.rst)
endif()

if(USE_FSAL_PROXY_V3)
    list(APPEND man_srcs
        gnfs-proxy-v3-config.rst)
endif()

if(USE_FSAL_GPFS)
   list(APPEND man_srcs
        gnfs-gpfs-config.rst)
endif()

if(USE_UDS_RECOV)
	list(APPEND man_srcs
	     gnfs-uds-cluster-design.rst
	     gnfs-uds-grace.rst)
endif()

foreach(man ${man_srcs})
  list(APPEND sphinx_input ${CMAKE_CURRENT_SOURCE_DIR}/${man})
  string(REGEX REPLACE ".rst$" "" cmd ${man})
  list(APPEND sphinx_output ${sphinx_output_dir}/${cmd}.8)
  install(FILES ${sphinx_output_dir}/${cmd}.8
          DESTINATION ${CMAKE_INSTALL_PREFIX}/share/man/man8/)
endforeach()

add_custom_command(
  OUTPUT ${sphinx_output}
  COMMAND ${SPHINX_BUILD} -b man -t man -d ${CMAKE_BINARY_DIR}/doc/doctrees -c ${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_CURRENT_SOURCE_DIR} ${sphinx_output_dir}
  DEPENDS ${sphinx_input})

add_custom_target(
  manpages ALL
  DEPENDS ${sphinx_output}
  COMMENT "manpages building")

